<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import FooterSection from './components/FooterSection.vue'
import SparklesText from './components/ui/SparklesText.vue'
import AnimatedGridPattern from './components/ui/AnimatedGridPattern.vue'
import BorderBeam from './components/ui/BorderBeam.vue'
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-middle-earth-gold/20 relative overflow-hidden">
      <!-- Animated Grid Background -->
      <AnimatedGridPattern
        :numSquares="20"
        :maxOpacity="0.1"
        :duration="3"
        :repeatDelay="1"
        class="absolute inset-0 h-full w-full [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
      />

      <!-- Border Beam Effect -->
      <BorderBeam class="absolute inset-0" />

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <RouterLink to="/" class="block">
              <SparklesText
                class="text-2xl font-elvish font-bold text-middle-earth-gold hover:text-yellow-400 transition-colors"
                :sparklesCount="8"
                :colors="{ first: '#D4AF37', second: '#FFD700' }"
              >
                Middle Earth
              </SparklesText>
            </RouterLink>
          </div>
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-4">
              <RouterLink to="/" class="nav-link">Home</RouterLink>
              <a href="#locations" class="nav-link">Locations</a>
              <a href="#testimonials" class="nav-link">Stories</a>
              <a href="#features" class="nav-link">Features</a>
              <RouterLink to="/about" class="nav-link">About</RouterLink>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
      <RouterView />
    </main>

    <!-- Footer -->
    <FooterSection />
  </div>
</template>

<style scoped>
/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Enhanced navigation link styles */
.nav-link {
  color: #d1d5db;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  background-size: 200% 100%;
  background-position: 100% 0;
}

.nav-link:hover {
  color: #D4AF37;
  background-position: 0% 0;
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  transform: translateY(-1px);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}
</style>
