<template>
  <svg
    ref="containerRef"
    aria-hidden="true"
    :class="cn(
      'pointer-events-none absolute inset-0 h-full w-full',
      className
    )"
    style="fill: rgba(156, 163, 175, 0.3); stroke: rgba(156, 163, 175, 0.3);"
    v-bind="$attrs"
  >
    <defs>
      <pattern
        :id="id"
        :width="width"
        :height="height"
        patternUnits="userSpaceOnUse"
        :x="x"
        :y="y"
      >
        <path
          :d="`M.5 ${height}V.5H${width}`"
          fill="none"
          :stroke-dasharray="strokeDasharray"
        />
      </pattern>
    </defs>
    <rect width="100%" height="100%" :fill="`url(#${id})`" />
    <svg :x="x" :y="y" class="overflow-visible">
      <rect
        v-for="(square, index) in squares"
        :key="`${square.pos[0]}-${square.pos[1]}-${index}`"
        :width="width - 1"
        :height="height - 1"
        :x="square.pos[0] * width + 1"
        :y="square.pos[1] * height + 1"
        fill="currentColor"
        stroke-width="0"
        :style="{
          opacity: 0,
          animation: `grid-fade-${square.id} ${duration}s ease-in-out ${index * 0.1}s`,
          animationIterationCount: 1,
          animationDirection: 'alternate'
        }"
      />
    </svg>
  </svg>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'

interface Square {
  id: number
  pos: [number, number]
}

interface Props {
  width?: number
  height?: number
  x?: number
  y?: number
  strokeDasharray?: number
  numSquares?: number
  className?: string
  maxOpacity?: number
  duration?: number
  repeatDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  width: 40,
  height: 40,
  x: -1,
  y: -1,
  strokeDasharray: 0,
  numSquares: 50,
  className: '',
  maxOpacity: 0.5,
  duration: 4,
  repeatDelay: 0.5
})

const containerRef = ref<SVGElement | null>(null)
const dimensions = ref({ width: 0, height: 0 })
const squares = ref<Square[]>([])

const id = computed(() => `grid-pattern-${Math.random().toString(36).substr(2, 9)}`)

const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ')
}

const getPos = (): [number, number] => {
  return [
    Math.floor((Math.random() * dimensions.value.width) / props.width),
    Math.floor((Math.random() * dimensions.value.height) / props.height)
  ]
}

const generateSquares = (count: number): Square[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    pos: getPos()
  }))
}

const updateSquarePosition = (id: number) => {
  squares.value = squares.value.map((sq) =>
    sq.id === id
      ? {
          ...sq,
          pos: getPos()
        }
      : sq
  )
}

let resizeObserver: ResizeObserver | null = null
let animationTimeouts: NodeJS.Timeout[] = []

onMounted(async () => {
  await nextTick()
  
  if (containerRef.value) {
    // Initialize dimensions
    const rect = containerRef.value.getBoundingClientRect()
    dimensions.value = {
      width: rect.width,
      height: rect.height
    }
    
    // Generate initial squares
    squares.value = generateSquares(props.numSquares)
    
    // Setup resize observer
    resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        dimensions.value = {
          width: entry.contentRect.width,
          height: entry.contentRect.height
        }
      }
    })
    
    resizeObserver.observe(containerRef.value)
    
    // Add CSS animations
    const style = document.createElement('style')
    style.textContent = squares.value.map(square => `
      @keyframes grid-fade-${square.id} {
        0% { opacity: 0; }
        50% { opacity: ${props.maxOpacity}; }
        100% { opacity: 0; }
      }
    `).join('\n')
    document.head.appendChild(style)
    
    // Setup animation cycle
    const startAnimationCycle = () => {
      squares.value.forEach((square, index) => {
        const timeout = setTimeout(() => {
          updateSquarePosition(square.id)
          // Restart the animation cycle for this square
          setTimeout(startAnimationCycle, props.duration * 1000 + props.repeatDelay * 1000)
        }, props.duration * 1000 + index * 100)
        animationTimeouts.push(timeout)
      })
    }
    
    startAnimationCycle()
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  animationTimeouts.forEach(timeout => clearTimeout(timeout))
})
</script>

<style scoped>
/* Base animation styles are generated dynamically */
</style>
