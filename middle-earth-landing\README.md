# 🏔️ Middle Earth Adventures Landing Page

A stunning, immersive landing page for Middle Earth Adventures - your gateway to the most magical realm ever created. Built with Vue.js 3, TypeScript, and Tailwind CSS, featuring beautiful atmospheric backgrounds and responsive design.

![Middle Earth Adventures](https://img.shields.io/badge/Middle%20Earth-Adventures-green?style=for-the-badge&logo=vue.js)
![Vue.js](https://img.shields.io/badge/Vue.js-3.x-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)

## ✨ Features

### 🎨 **Stunning Visual Design**
- **Atmospheric Background Images**: Custom Middle Earth location artwork for each section
- **Immersive Experience**: The Shire, Rivendell, Rohan, Gondor, and Mordor landscapes
- **Perfect Text Readability**: Carefully balanced opacity and gradient overlays
- **Smooth Hover Effects**: Interactive location cards with opacity transitions

### 🏗️ **Modern Architecture**
- **Vue.js 3 Composition API**: Latest Vue.js features with TypeScript support
- **Component-Based Design**: Modular, reusable components for easy maintenance
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **Responsive Design**: Optimized for all screen sizes and devices

### 🎯 **Interactive Elements**
- **Hero Section**: Compelling call-to-action with adventure request form
- **Location Cards**: Explore iconic Middle Earth destinations
- **Testimonials**: Reviews from legendary characters like Frodo, Gandalf, and Aragorn
- **Features Showcase**: Interactive journey planner and comprehensive services

### 🛠️ **Developer Experience**
- **TypeScript**: Full type safety and excellent IDE support
- **ESLint + Prettier**: Code quality and consistent formatting
- **Vitest**: Fast unit testing framework
- **Playwright**: End-to-end testing capabilities
- **Hot Module Replacement**: Instant development feedback

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/PapaBear1981/mid-earth.git
cd mid-earth

# Install dependencies
npm install

# Start development server
npm run dev
```

Visit `http://localhost:5174` to see your Middle Earth adventure begin! 🧙‍♂️

## 📁 Project Structure

```
middle-earth-landing/
├── public/
│   ├── images/           # Middle Earth location artwork
│   │   ├── gondor.png
│   │   ├── mordor.png
│   │   ├── rivendale.png
│   │   ├── rohan.png
│   │   └── shire.png
│   └── favicon.ico
├── src/
│   ├── components/       # Vue components
│   │   ├── HeroSection.vue
│   │   ├── LocationCards.vue
│   │   ├── TestimonialsSection.vue
│   │   ├── FeaturesSection.vue
│   │   └── FooterSection.vue
│   ├── views/           # Page views
│   ├── router/          # Vue Router configuration
│   ├── stores/          # Pinia state management
│   └── assets/          # Static assets
└── ...config files
```

## 🎨 Design Philosophy

This landing page creates an immersive Middle Earth experience through:

- **Atmospheric Backgrounds**: Each section features location-specific artwork that enhances the storytelling
- **Balanced Opacity**: Images are visible enough to create atmosphere while maintaining excellent text readability
- **Thematic Consistency**: Color schemes and styling reflect the magical, epic nature of Middle Earth
- **User Experience**: Smooth interactions and clear navigation guide users through their adventure journey

## 🛠️ Available Scripts

### Development
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build locally
```

### Code Quality
```bash
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking
```

### Testing
```bash
npm run test:unit    # Run unit tests with Vitest
npm run test:e2e     # Run end-to-end tests with Playwright
```

## 🎯 Key Components

### HeroSection.vue
- **Shire Background**: Peaceful, welcoming atmosphere
- **Adventure Request Form**: Interactive form for user engagement
- **Call-to-Action Buttons**: Primary and secondary action buttons

### LocationCards.vue
- **Rivendell Section Background**: Mystical elven atmosphere
- **Individual Location Cards**: Shire, Rivendell, Rohan, Gondor with hover effects
- **Responsive Grid**: Adapts to different screen sizes

### TestimonialsSection.vue
- **Mordor Background**: Dramatic, epic atmosphere
- **Character Reviews**: Testimonials from iconic Middle Earth characters
- **Trust Indicators**: Statistics and social proof

### FeaturesSection.vue
- **Gondor Background**: Regal, authoritative atmosphere
- **Interactive Journey Planner**: Engaging user experience element
- **Service Highlights**: Comprehensive feature showcase

## 🌟 Customization

### Adding New Locations
1. Add your image to `public/images/`
2. Update the location cards in `LocationCards.vue`
3. Adjust opacity levels for optimal visibility

### Modifying Backgrounds
Each section's background opacity can be adjusted in the respective component:
```vue
<!-- Example: Adjusting opacity -->
<img src="/images/your-image.png" class="opacity-30" />
```

### Color Scheme
The project uses Tailwind CSS. Modify `tailwind.config.js` to customize:
- Color palette
- Typography
- Spacing
- Breakpoints

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

The `dist/` folder contains the production-ready files.

### Deployment Options
- **Netlify**: Drag and drop the `dist` folder
- **Vercel**: Connect your GitHub repository
- **GitHub Pages**: Use GitHub Actions for automatic deployment
- **Traditional Hosting**: Upload `dist` folder contents

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **J.R.R. Tolkien** - For creating the incredible world of Middle Earth
- **Vue.js Team** - For the amazing framework
- **Tailwind CSS** - For the utility-first CSS framework
- **Middle Earth Artists** - For the beautiful location artwork

---

*"Not all those who wander are lost."* - J.R.R. Tolkien

Ready to begin your Middle Earth adventure? 🧙‍♂️✨
