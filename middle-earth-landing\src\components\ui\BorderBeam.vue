<template>
  <div
    :class="cn(
      'pointer-events-none absolute inset-0 rounded-[inherit] [border:calc(var(--border-width,1px)*1px)_solid_transparent]',
      className
    )"
    :style="{
      '--size': size,
      '--duration': `${duration}s`,
      '--anchor': anchor,
      '--border-width': borderWidth,
      '--color-from': colorFrom,
      '--color-to': colorTo,
      '--delay': `-${delay}s`,
      background: `
        linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
        conic-gradient(
          from calc(var(--anchor) * 1deg),
          transparent 0deg,
          var(--color-from) calc(var(--size) * 1deg),
          var(--color-to) calc(var(--size) * 2deg),
          transparent calc(var(--size) * 3deg)
        )
      `,
      backgroundSize: '100% 100%, calc(100% - 2px) calc(100% - 2px)',
      backgroundPosition: '0 0, 1px 1px',
      backgroundRepeat: 'no-repeat',
      animation: `border-beam var(--duration) infinite linear var(--delay)`,
      maskComposite: 'subtract'
    }"
  />
</template>

<script setup lang="ts">
interface Props {
  className?: string
  size?: number
  duration?: number
  anchor?: number
  borderWidth?: number
  colorFrom?: string
  colorTo?: string
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
  size: 200,
  duration: 15,
  anchor: 90,
  borderWidth: 1.5,
  colorFrom: '#D4AF37',
  colorTo: '#FFD700',
  delay: 0
})

const cn = (...classes: (string | undefined)[]) => {
  return classes.filter(Boolean).join(' ')
}
</script>

<style scoped>
@keyframes border-beam {
  0% {
    background: 
      linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
      conic-gradient(
        from calc(var(--anchor) * 1deg),
        transparent 0deg,
        var(--color-from) calc(var(--size) * 1deg),
        var(--color-to) calc(var(--size) * 2deg),
        transparent calc(var(--size) * 3deg)
      );
  }
  25% {
    background: 
      linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
      conic-gradient(
        from calc((var(--anchor) + 90) * 1deg),
        transparent 0deg,
        var(--color-from) calc(var(--size) * 1deg),
        var(--color-to) calc(var(--size) * 2deg),
        transparent calc(var(--size) * 3deg)
      );
  }
  50% {
    background: 
      linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
      conic-gradient(
        from calc((var(--anchor) + 180) * 1deg),
        transparent 0deg,
        var(--color-from) calc(var(--size) * 1deg),
        var(--color-to) calc(var(--size) * 2deg),
        transparent calc(var(--size) * 3deg)
      );
  }
  75% {
    background: 
      linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
      conic-gradient(
        from calc((var(--anchor) + 270) * 1deg),
        transparent 0deg,
        var(--color-from) calc(var(--size) * 1deg),
        var(--color-to) calc(var(--size) * 2deg),
        transparent calc(var(--size) * 3deg)
      );
  }
  100% {
    background: 
      linear-gradient(var(--anchor), var(--color-from), var(--color-to)),
      conic-gradient(
        from calc((var(--anchor) + 360) * 1deg),
        transparent 0deg,
        var(--color-from) calc(var(--size) * 1deg),
        var(--color-to) calc(var(--size) * 2deg),
        transparent calc(var(--size) * 3deg)
      );
  }
}
</style>
